<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="eb9cff37-63d0-4527-9d8d-ef435acd7e47" name="更改" comment="feat(model): 增加模型相关的审计日志、资源管理功能;&#10;fix(swagger): 修复Swagger无法显示问题&#10;&#10;- 在 audit_log.py 中添加 MODEL 类型的审计日志&#10;- 在 group_resource.py 中更新资源类别的描述，支持模型资源&#10;- 在 main.py 中添加健康检查和前端服务路由&#10;- 在 role_access.py 中增加模型读写权限&#10;- 更新 role_group_service.py，支持模型资源的获取&#10;- 在 tag.py 中添加模型资源的标签支持">
      <change beforePath="$PROJECT_DIR$/src/backend/bisheng/interface/embeddings/custom.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/backend/bisheng/interface/embeddings/custom.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/backend/bisheng/interface/initialize/loading.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/backend/bisheng/interface/initialize/loading.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/backend/bisheng/worker/__init__.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/backend/bisheng/worker/__init__.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/Programs/Python/Python310/Lib/site-packages/starlette/middleware/cors.py" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="31GYmITnnSFNqfXqkpjubub13BX" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Python.main.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Shell Script.【优先启动】若干个celery工作流线程队列.executor": "Run",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/projects/postwise-v2.0/postwise/src/backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.postwise-frontend-service.executor": "Run",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "ts.external.directory.path": "D:\\projects\\postwise-v2.0\\postwise\\src\\frontend\\platform\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "com.intellij.ide.scratch.LRUPopupBuilder$1/更改 SQL 方言": [
      "MySQL"
    ]
  }
}]]></component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="postwise" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/backend/bisheng" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/backend/bisheng/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="postwise-frontend-service" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/src/frontend/platform/package.json" />
      <command value="run" />
      <scripts>
        <script value="start" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.postwise-frontend-service" />
      <item itemvalue="Python.main" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.main" />
        <item itemvalue="Python.main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.17011.127" />
        <option value="bundled-python-sdk-48aec45f0201-7e9c3bbb6e34-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.17011.127" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="eb9cff37-63d0-4527-9d8d-ef435acd7e47" name="更改" comment="" />
      <created>1755152034140</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755152034140</updated>
      <workItem from="1755152035546" duration="10674000" />
      <workItem from="1755239075568" duration="149000" />
      <workItem from="1755240156713" duration="20000" />
      <workItem from="1755242668346" duration="32000" />
      <workItem from="1755243830491" duration="2031000" />
      <workItem from="1755194685557" duration="4594000" />
      <workItem from="1755478534208" duration="12130000" />
      <workItem from="1755584029759" duration="9742000" />
      <workItem from="1755677417749" duration="12363000" />
      <workItem from="1755741791427" duration="18111000" />
      <workItem from="1755776640108" duration="6635000" />
      <workItem from="1755823566887" duration="5016000" />
      <workItem from="1755845915286" duration="9822000" />
      <workItem from="1756082804275" duration="9449000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 更新第一版的 v2.0.0 &#10;&#10;- 新增 .drone.yml 文件，配置 Docker 和 SSH 部署步骤&#10;- 添加多个 .gitignore 文件，用于不同目录的文件忽略&#10;- 新增 .gitmodules 文件，定义子模块路径和 URL- 添加 .pre-commit-config.yaml 文件，配置代码提交前的检查规则&#10;- 新增多个 Markdown 文件，记录股东结构、注册资本等信息">
      <option name="closed" value="true" />
      <created>1755152880048</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755152880050</updated>
    </task>
    <task id="LOCAL-00002" summary="fix: 修复启动问题；同时移除不需要的依赖。&#10;&#10;1. （最主要的）修复了 fastapi-jwt-auth 与 pydantic v2 的兼容性问题，即手动修补了 config.py 文件，在我的电脑上的位置是：&#10;【C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi_jwt_auth\config.py】&#10;&#10;具体的代码层面：&#10;1. 类型注解：Sequence[StrictStr] → List[StrictStr]&#10;2. Validator 装饰器：移除 each_item=True 参数&#10;3. Validator 逻辑：手动添加 isinstance() 检查和 for 循环来验证每个项目&#10;4. 配置类：min_anystr_length → str_min_length，anystr_strip_whitespace → str_strip_whitespace&#10;&#10;2. 修复了 pyproject.toml 配置问题：&#10;- 将过时的 [tool.poetry.dev-dependencies] 更新为 [tool.poetry.group.dev.dependencies]&#10;&#10;- 移除了有问题的 langchain-serve extra 依赖">
      <option name="closed" value="true" />
      <created>1755157522387</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755157522387</updated>
    </task>
    <task id="LOCAL-00003" summary="docs(backend): 更新 README 文档以包含后端本地开发指南&#10;&#10;- 新增后端配置文件说明，指导如何修改 MySQL、Redis、Milvus、ES 和 MinIO 配置&#10;- 添加下载依赖和启动项目的详细步骤说明&#10;- 更新项目名称为&quot;邮智&quot;，替换原&quot;毕昇&quot;名称">
      <option name="closed" value="true" />
      <created>1755157855394</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755157855394</updated>
    </task>
    <task id="LOCAL-00004" summary="build：修改官方的后端Dockerfile文件，改成公司镜像，添加国内镜像源。&#10;&#10;- 利用 Docker 缓存通过首先复制依赖文件&#10;- 配置 poetry 使用国内镜像源以加速下载&#10;- 优化补丁应用过程&#10;- 添加执行权限设置以确保脚本可执行">
      <option name="closed" value="true" />
      <created>1755158426718</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755158426718</updated>
    </task>
    <task id="LOCAL-00005" summary="docs(backend): 补充 fastapi-jwt-auth 启动报错解决方案&#10;&#10;- 新增解决方案文档，详细说明 fastapi-jwt-auth 的修复方法&#10;- 在 README 中添加报错提示和解决方案文档的引用">
      <option name="closed" value="true" />
      <created>1755158604387</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755158604387</updated>
    </task>
    <task id="LOCAL-00006" summary="build(frontend): 更新 Dockerfile以使用私有镜像仓库&#10;&#10;- 将 node 和 nginx镜像来源从官方更改为私有镜像仓库 reg.un-net.com&#10;- 保留了原有的构建逻辑和配置">
      <option name="closed" value="true" />
      <created>1755160233600</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755160233600</updated>
    </task>
    <task id="LOCAL-00007" summary="chore: 添加启动应用服务器、Celery Worker和前端应用的批处理脚本&#10;&#10;- 新增 start_app.cmd、start_celery.cmd 和 start_frontend.cmd 文件&#10;- 实现了启动不同服务的自动化脚本&#10;- 增加了目录存在性检查和错误提示功能&#10;- 优化了启动命令的显示和说明">
      <option name="closed" value="true" />
      <created>1755160256346</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755160256346</updated>
    </task>
    <task id="LOCAL-00008" summary="feat(deploy): 添加 main 分支下， Docker 部署 postwise 的脚本文件。&#10;&#10;- 支持选择性构建【前端】和【后端】镜像&#10;- 自动更新 docker-compose.yml 版本号（暂时默认v2.0.0）&#10;- 部署过程中检查 SSH 连接并拉取最新代码&#10;- 部署完成后检查服务状态并记录部署日志">
      <option name="closed" value="true" />
      <created>1755248215974</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755248215974</updated>
    </task>
    <task id="LOCAL-00009" summary="feat(database): 新增知识库标签字段并扩展服务描述长度&#10;&#10;- 在 knowledge 表中添加 tags 字段，用于区分不同的知识库- 修改 llm_server 表中 description 字段的长度，支持更详细的描述&#10;- 更新相关模型定义，以适应数据库结构的变更">
      <option name="closed" value="true" />
      <created>1755698003047</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755698003047</updated>
    </task>
    <task id="LOCAL-00010" summary="feat(model): 添加模型【标签】功能并调整数据库结构&#10;&#10;- 在 llm_model 表中添加 tags 字段，用于存储模型的上下文长度、参数量以及量化精度等参数。&#10;&#10;- 修改 llm_server 表，将 description 字段长度扩展为 text 类型&#10;&#10;- 更新后端模型定义和 API 模式，支持模型标签功能&#10;&#10;- 调整版本号输入逻辑，增加默认值选项">
      <option name="closed" value="true" />
      <created>1755744687782</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755744687782</updated>
    </task>
    <task id="LOCAL-00011" summary="docs(data): 更新数据库变更记录- 新增数据库变更记录的说明和使用示例&#10;- 在 knowledge 表中添加 tags 字段，用于区分不同类型的数据库&#10;- 更新 llm_model 表，为不同模型添加相应的标签&#10;- 添加 Qwen3 系列、Qwen2.5-VL 系列等模型的标签">
      <option name="closed" value="true" />
      <created>1755755922162</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755755922162</updated>
    </task>
    <task id="LOCAL-00012" summary="feat(workflow): 添加节点调试信息和用户ID、工作流ID参数&#10;&#10;- 在节点执行过程中添加了调试信息打印，便于排查问题&#10;- 在 StartNode 中添加了用户ID和工作流ID的生成和传递&#10;- 更新了前端 API以支持新的用户ID和工作流ID参数">
      <option name="closed" value="true" />
      <created>1755767695887</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755767695887</updated>
    </task>
    <task id="LOCAL-00013" summary="refactor(bisheng): 修正 start 节点返回值的 key 名称&#10;&#10;- 将 'user_id' 的值从 'current_time' 改为正确的 'user_id'&#10;- 将 'workflow_id' 的 key 名称改为 'app_id'&#10;- 更新前端 API 相应地将 'workflow_id'改为 'app_id'&#10;&#10;此修改确保了 start 节点返回值的正确性和一致性，以及前后端接口的同步">
      <option name="closed" value="true" />
      <created>1755777250280</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755777250280</updated>
    </task>
    <task id="LOCAL-00014" summary="fix(worker): 修复 Celery应用实例不统一问题&#10;&#10;- 在 worker/__init__.py 中添加 __getattr__ 方法，动态导出 celery_app&#10;- 更新 start_celery.cmd，指定多个队列以解决任务分配问题">
      <option name="closed" value="true" />
      <created>1755785059610</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755785059610</updated>
    </task>
    <task id="LOCAL-00015" summary="build:移除 start_celery.cmd 中的队列参数移除了 start_celery.cmd 文件中的 -Q 参数，该参数指定了 Celery worker 应该消费的队列。&#10;现在，该命令将使用默认队列而不是指定的 workflow_celery, knowledge_celery, celery 队列。">
      <option name="closed" value="true" />
      <created>1755850492376</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755850492376</updated>
    </task>
    <task id="LOCAL-00016" summary="refactor(worker): 注释添加和代码整理&#10;&#10;- 在 worker/__init__.py 文件中添加了关于 Celery 应用实例问题的注释&#10;- 注释了可能导致连接问题的两个 Celery 应用实例&#10;- 添加了导入 celery_app 的相关注释&#10;- 注释了不应用于线上环境的代码段">
      <option name="closed" value="true" />
      <created>1755852466711</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755852466711</updated>
    </task>
    <task id="LOCAL-00017" summary="feat(model): 增加模型相关的审计日志、资源管理功能;&#10;fix(swagger): 修复Swagger无法显示问题&#10;&#10;- 在 audit_log.py 中添加 MODEL 类型的审计日志&#10;- 在 group_resource.py 中更新资源类别的描述，支持模型资源&#10;- 在 main.py 中添加健康检查和前端服务路由&#10;- 在 role_access.py 中增加模型读写权限&#10;- 更新 role_group_service.py，支持模型资源的获取&#10;- 在 tag.py 中添加模型资源的标签支持">
      <option name="closed" value="true" />
      <created>1756088577268</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1756088577268</updated>
    </task>
    <option name="localTasksCounter" value="18" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="feat: 更新第一版的 v2.0.0 &#10;&#10;- 新增 .drone.yml 文件，配置 Docker 和 SSH 部署步骤&#10;- 添加多个 .gitignore 文件，用于不同目录的文件忽略&#10;- 新增 .gitmodules 文件，定义子模块路径和 URL- 添加 .pre-commit-config.yaml 文件，配置代码提交前的检查规则&#10;- 新增多个 Markdown 文件，记录股东结构、注册资本等信息" />
    <MESSAGE value="fix: 修复启动问题；同时移除不需要的依赖。&#10;&#10;1. （最主要的）修复了 fastapi-jwt-auth 与 pydantic v2 的兼容性问题，即手动修补了 config.py 文件，在我的电脑上的位置是：&#10;【C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi_jwt_auth\config.py】&#10;&#10;具体的代码层面：&#10;1. 类型注解：Sequence[StrictStr] → List[StrictStr]&#10;2. Validator 装饰器：移除 each_item=True 参数&#10;3. Validator 逻辑：手动添加 isinstance() 检查和 for 循环来验证每个项目&#10;4. 配置类：min_anystr_length → str_min_length，anystr_strip_whitespace → str_strip_whitespace&#10;&#10;2. 修复了 pyproject.toml 配置问题：&#10;- 将过时的 [tool.poetry.dev-dependencies] 更新为 [tool.poetry.group.dev.dependencies]&#10;&#10;- 移除了有问题的 langchain-serve extra 依赖" />
    <MESSAGE value="docs(backend): 更新 README 文档以包含后端本地开发指南&#10;&#10;- 新增后端配置文件说明，指导如何修改 MySQL、Redis、Milvus、ES 和 MinIO 配置&#10;- 添加下载依赖和启动项目的详细步骤说明&#10;- 更新项目名称为&quot;邮智&quot;，替换原&quot;毕昇&quot;名称" />
    <MESSAGE value="build：修改官方的后端Dockerfile文件，改成公司镜像，添加国内镜像源。&#10;&#10;- 利用 Docker 缓存通过首先复制依赖文件&#10;- 配置 poetry 使用国内镜像源以加速下载&#10;- 优化补丁应用过程&#10;- 添加执行权限设置以确保脚本可执行" />
    <MESSAGE value="docs(backend): 补充 fastapi-jwt-auth 启动报错解决方案&#10;&#10;- 新增解决方案文档，详细说明 fastapi-jwt-auth 的修复方法&#10;- 在 README 中添加报错提示和解决方案文档的引用" />
    <MESSAGE value="build(frontend): 更新 Dockerfile以使用私有镜像仓库&#10;&#10;- 将 node 和 nginx镜像来源从官方更改为私有镜像仓库 reg.un-net.com&#10;- 保留了原有的构建逻辑和配置" />
    <MESSAGE value="chore: 添加启动应用服务器、Celery Worker和前端应用的批处理脚本&#10;&#10;- 新增 start_app.cmd、start_celery.cmd 和 start_frontend.cmd 文件&#10;- 实现了启动不同服务的自动化脚本&#10;- 增加了目录存在性检查和错误提示功能&#10;- 优化了启动命令的显示和说明" />
    <MESSAGE value="feat(deploy): 添加 main 分支下， Docker 部署 postwise 的脚本文件。&#10;&#10;- 支持选择性构建【前端】和【后端】镜像&#10;- 自动更新 docker-compose.yml 版本号（暂时默认v2.0.0）&#10;- 部署过程中检查 SSH 连接并拉取最新代码&#10;- 部署完成后检查服务状态并记录部署日志" />
    <MESSAGE value="feat(database): 新增知识库标签字段并扩展服务描述长度&#10;&#10;- 在 knowledge 表中添加 tags 字段，用于区分不同的知识库- 修改 llm_server 表中 description 字段的长度，支持更详细的描述&#10;- 更新相关模型定义，以适应数据库结构的变更" />
    <MESSAGE value="feat(model): 添加模型【标签】功能并调整数据库结构&#10;&#10;- 在 llm_model 表中添加 tags 字段，用于存储模型的上下文长度、参数量以及量化精度等参数。&#10;&#10;- 修改 llm_server 表，将 description 字段长度扩展为 text 类型&#10;&#10;- 更新后端模型定义和 API 模式，支持模型标签功能&#10;&#10;- 调整版本号输入逻辑，增加默认值选项" />
    <MESSAGE value="docs(data): 更新数据库变更记录- 新增数据库变更记录的说明和使用示例&#10;- 在 knowledge 表中添加 tags 字段，用于区分不同类型的数据库&#10;- 更新 llm_model 表，为不同模型添加相应的标签&#10;- 添加 Qwen3 系列、Qwen2.5-VL 系列等模型的标签" />
    <MESSAGE value="feat(workflow): 添加节点调试信息和用户ID、工作流ID参数&#10;&#10;- 在节点执行过程中添加了调试信息打印，便于排查问题&#10;- 在 StartNode 中添加了用户ID和工作流ID的生成和传递&#10;- 更新了前端 API以支持新的用户ID和工作流ID参数" />
    <MESSAGE value="refactor(bisheng): 修正 start 节点返回值的 key 名称&#10;&#10;- 将 'user_id' 的值从 'current_time' 改为正确的 'user_id'&#10;- 将 'workflow_id' 的 key 名称改为 'app_id'&#10;- 更新前端 API 相应地将 'workflow_id'改为 'app_id'&#10;&#10;此修改确保了 start 节点返回值的正确性和一致性，以及前后端接口的同步" />
    <MESSAGE value="fix(worker): 修复 Celery应用实例不统一问题&#10;&#10;- 在 worker/__init__.py 中添加 __getattr__ 方法，动态导出 celery_app&#10;- 更新 start_celery.cmd，指定多个队列以解决任务分配问题" />
    <MESSAGE value="build:移除 start_celery.cmd 中的队列参数移除了 start_celery.cmd 文件中的 -Q 参数，该参数指定了 Celery worker 应该消费的队列。&#10;现在，该命令将使用默认队列而不是指定的 workflow_celery, knowledge_celery, celery 队列。" />
    <MESSAGE value="refactor(worker): 注释添加和代码整理&#10;&#10;- 在 worker/__init__.py 文件中添加了关于 Celery 应用实例问题的注释&#10;- 注释了可能导致连接问题的两个 Celery 应用实例&#10;- 添加了导入 celery_app 的相关注释&#10;- 注释了不应用于线上环境的代码段" />
    <MESSAGE value="feat(model): 增加模型相关的审计日志、资源管理功能;&#10;fix(swagger): 修复Swagger无法显示问题&#10;&#10;- 在 audit_log.py 中添加 MODEL 类型的审计日志&#10;- 在 group_resource.py 中更新资源类别的描述，支持模型资源&#10;- 在 main.py 中添加健康检查和前端服务路由&#10;- 在 role_access.py 中增加模型读写权限&#10;- 更新 role_group_service.py，支持模型资源的获取&#10;- 在 tag.py 中添加模型资源的标签支持" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(model): 增加模型相关的审计日志、资源管理功能;&#10;fix(swagger): 修复Swagger无法显示问题&#10;&#10;- 在 audit_log.py 中添加 MODEL 类型的审计日志&#10;- 在 group_resource.py 中更新资源类别的描述，支持模型资源&#10;- 在 main.py 中添加健康检查和前端服务路由&#10;- 在 role_access.py 中增加模型读写权限&#10;- 更新 role_group_service.py，支持模型资源的获取&#10;- 在 tag.py 中添加模型资源的标签支持" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/backend/bisheng/api/v1/workflow.py</url>
          <line>100</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/backend/bisheng/api/v1/flows.py</url>
          <line>32</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/postwise$main.coverage" NAME="main 覆盖结果" MODIFIED="1756104497031" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/backend/bisheng" />
  </component>
</project>